package com.varabyte.kobweb.site.pages

import androidx.compose.runtime.Composable
import com.varabyte.kobweb.compose.foundation.layout.Column
import com.varabyte.kobweb.compose.ui.Alignment
import com.varabyte.kobweb.compose.ui.Modifier
import com.varabyte.kobweb.compose.ui.modifiers.width
import com.varabyte.kobweb.core.Page
import com.varabyte.kobweb.site.components.layouts.PageLayout
import com.varabyte.kobweb.site.components.sections.login_signup.LoginSignUpTabs
import org.jetbrains.compose.web.css.percent

@Page(routeOverride = "login-signup")
@Composable
fun Signup() {
    PageLayout("Login SignUp") {
        Column(
            Modifier.width(100.percent),
            horizontalAlignment = Alignment.CenterHorizontally,
        ){
            LoginSignUpTabs()
        }
    }
}