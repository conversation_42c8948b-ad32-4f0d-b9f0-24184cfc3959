package com.varabyte.kobweb.site.components.sections.login_signup

import androidx.compose.runtime.*
import com.varabyte.kobweb.compose.css.ObjectFit
import com.varabyte.kobweb.compose.foundation.layout.Box
import com.varabyte.kobweb.compose.foundation.layout.Column
import com.varabyte.kobweb.compose.foundation.layout.Row
import com.varabyte.kobweb.compose.foundation.layout.Spacer
import com.varabyte.kobweb.compose.style.KobwebComposeStyleSheet.style
import com.varabyte.kobweb.compose.ui.Alignment
import com.varabyte.kobweb.compose.ui.Modifier
import com.varabyte.kobweb.compose.ui.graphics.Color
import com.varabyte.kobweb.compose.ui.modifiers.*
import com.varabyte.kobweb.silk.components.disclosure.Tabs
import com.varabyte.kobweb.silk.style.CssStyle
import com.varabyte.kobweb.silk.style.base
import org.jetbrains.compose.web.attributes.InputType
import org.jetbrains.compose.web.attributes.placeholder
import org.jetbrains.compose.web.css.*
import org.jetbrains.compose.web.dom.Button
import org.jetbrains.compose.web.dom.Img
import org.jetbrains.compose.web.dom.Input
import org.jetbrains.compose.web.dom.Text

@Composable
fun LoginSignUpTabs() {

    var selectedIndex by remember {
        mutableIntStateOf(-1)
    }

    Column {

        Tabs(
            onTabSelected = { index ->
                selectedIndex = index
            },
        ) {
            TabPanel {
                Tab(
                    Modifier.then(
                        TabsNavStyleModifier
                    )
                ) { Text("Sign Up") }
                Panel { SignUpForm() }
            }
            TabPanel {
                Tab(
                    Modifier.then(
                        TabsNavStyleModifier
                    )
                ) { Text("Login") }
                Panel { LoginForm() }
            }
        }
    }
}

@Composable
fun SignUpForm() {
    Column(
        modifier = Modifier.width(100.percent).padding(16.px),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Text("Sign Up")

        VerticalSpacer(16)

        SvgButtonWithUrl("https://ik.imagekit.io/ingpmow4t/Martaba%20Site%20Images/social-media-signup-login.svg?updatedAt=1733325841030")

        SvgButtonWithUrl("https://ik.imagekit.io/ingpmow4t/Martaba%20Site%20Images/social-media-signup-login%20(1).svg?updatedAt=1733326143382")

    }
}

@Composable
fun LoginForm() {
    Column(
        modifier = Modifier.width(100.percent).padding(16.px),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Text("Login")

        VerticalSpacer(16)

        SvgButtonWithUrl("https://ik.imagekit.io/ingpmow4t/Martaba%20Site%20Images/sign_in_facebook.svg?updatedAt=1733326756686")

        SvgButtonWithUrl("https://ik.imagekit.io/ingpmow4t/Martaba%20Site%20Images/sign_in_google.svg?updatedAt=1733326756731")

    }
}

@Composable
fun VerticalSpacer(height: Int) {
    Box(modifier = Modifier.height(height.px))
}

@Composable
fun HorizontalSpacer(width: Int) {
    Box(modifier = Modifier.width(width.px))
}

@Composable
fun SvgButtonWithUrl(url: String) {
    Img(src = url, attrs = {
        style {
            width(300.px)
            height(70.px)
            marginRight(8.px)
        }
    })
}

val TabsNavStyleModifier =
    Modifier
        .width(302.px)
        .height(56.px)
        .flexGrow(0)
        .objectFit(ObjectFit.Contain)
        .alignItems(AlignItems.Center)