package com.varabyte.kobweb.site.components.sections.home

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.varabyte.kobweb.compose.css.Cursor
import com.varabyte.kobweb.compose.css.FontStyle
import com.varabyte.kobweb.compose.css.Overflow
import com.varabyte.kobweb.compose.css.Visibility
import com.varabyte.kobweb.compose.foundation.layout.Arrangement
import com.varabyte.kobweb.compose.foundation.layout.Box
import com.varabyte.kobweb.compose.foundation.layout.Column
import com.varabyte.kobweb.compose.foundation.layout.Row
import com.varabyte.kobweb.compose.ui.Alignment
import com.varabyte.kobweb.compose.ui.Modifier
import com.varabyte.kobweb.compose.ui.graphics.Colors
import com.varabyte.kobweb.compose.ui.modifiers.*
import com.varabyte.kobweb.silk.components.icons.fa.FaBars
import com.varabyte.kobweb.silk.components.icons.fa.FaBusinessTime
import com.varabyte.kobweb.silk.components.icons.fa.FaCaretDown
import com.varabyte.kobweb.silk.components.icons.fa.FaChevronRight
import com.varabyte.kobweb.silk.components.text.SpanText
import com.varabyte.kobweb.silk.style.breakpoint.Breakpoint
import com.varabyte.kobweb.silk.theme.breakpoint.rememberBreakpoint
import org.jetbrains.compose.web.css.*

data class MenuCategory(
    val name: String,
    val subcategories: Map<String, List<String>> = emptyMap()
)

@Composable
fun TopNavMenu() {
    val breakpoint = rememberBreakpoint()
    var mobileMenuOpen by remember { mutableStateOf(false) }
    
    val categories = remember {
        listOf(
            MenuCategory("Animal Feed & Bye Products", mapOf(
                "Animal Feed Products" to listOf(),
                "Bye Products" to listOf("Rice Husk", "Sugar Cane bye products", "Soap Stock", "Acid Oil", 
                    "Soya Acid Oil", "Crushed Plastic", "Transformers' Used Oil"),
                "Pet Bottles" to listOf(),
                "Grain Products" to listOf()
            )),
            MenuCategory("Educational & Kids", mapOf(
                "Moallam ul Quran with pen" to listOf(),
                "Kids Collection" to listOf("Garments", "Toys", "Educational", "Watches", "Goggles", 
                    "School Bags", "Story Books", "Art & Craft", "Soft Toys", "Birthday Decorations")
            )),
            MenuCategory("Home & Garden", mapOf(
                "Home Decor" to listOf(),
                "Garden Decor / Gardening Items" to listOf(),
                "Bed Sheets" to listOf(),
                "Marble Show Pieces" to listOf(),
                "Himalayan Salt Products" to listOf("Lamps", "Paper weight", "Show Pieces")
            )),
            MenuCategory("Fashion & Accessories", mapOf(
                "Ladies Purse" to listOf(),
                "Mehendi Decorations" to listOf(),
                "Travelling Bags" to listOf(),
                "Gents" to listOf("Wallets", "Leather Belts", "Joggers", "Smart Watches", "Watches",
                    "Goggles", "Car Accessories", "Gift Packs")
            )),
            MenuCategory("Electronics", mapOf(
                "Mobile Accessories" to listOf(),
                "Laptop Accessories" to listOf()
            )),
            MenuCategory("Beauty & Personal Care", mapOf(
                "Cosmetics / Body Products" to listOf(),
                "Perfumes" to listOf("MTJ", "Scents & Stories", "J.", "Saeed Ghani")
            )),
            MenuCategory("Religious Items", mapOf(
                "Prayers Accessories" to listOf("Tasbeeh", "Prayer Carpet / Jae Namaz", "Prayer Chair", 
                    "Rehel", "Quran Box", "Abaya", "Ahraam")
            )),
            MenuCategory("Disposable Items", mapOf(
                "Disposible Items" to listOf("Paper Cups", "Paper Glass", "Paper Plates (Small / Medium / Large)")
            ))
        )
    }
    
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .backgroundColor(Colors.White)
            .padding(topBottom = 8.px, leftRight = if (breakpoint >= Breakpoint.MD) 50.px else 16.px)
            .borderBottom(1.px, LineStyle.Solid, Color("#e0e0e0"))
            .zIndex(100)
    ) {
        if (breakpoint >= Breakpoint.MD) {
            // Desktop Menu
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                categories.forEach { category ->
                    NavMenuItem(category)
                }
            }
        } else {
            // Mobile Menu Toggle
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (mobileMenuOpen) {
                    FaBusinessTime(
                        modifier = Modifier
                            .fontSize(24.px)
                            .cursor(Cursor.Pointer)
                            .color(Color("#4CAF50"))
                            .onClick { mobileMenuOpen = false }
                    )
                } else {
                    FaBars(
                        modifier = Modifier
                            .fontSize(24.px)
                            .cursor(Cursor.Pointer)
                            .color(Color("#4CAF50"))
                            .onClick { mobileMenuOpen = true }
                    )
                }
                
                SpanText(
                    "Categories",
                    modifier = Modifier
                        .color(Color("#333333"))
                        .fontSize(16.px)
                        .fontWeight(600)
                )
                
                // Empty spacer to balance the row
                Box(Modifier.width(24.px))
            }
            
            // Mobile Menu Dropdown
            if (mobileMenuOpen) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .position(Position.Absolute)
                        .top(100.percent)
                        .left(0.px)
                        .backgroundColor(Colors.White)
                        .boxShadow(0.px, 4.px, 8.px, 0.px, Color("#DD000000"))
                        .zIndex(1000)
                        .maxHeight(80.vh)
                        .overflow(Overflow.Auto)
                        .border(1.px, LineStyle.Solid, Color("#e0e0e0"))
                ) {
                    categories.forEach { category ->
                        MobileNavMenuItem(category)
                    }
                }
            }
        }
    }
}

@Composable
private fun NavMenuItem(category: MenuCategory) {
    var isHovered by remember { mutableStateOf(false) }
    
    Box(
        modifier = Modifier
            .position(Position.Relative)
            .onMouseEnter { isHovered = true }
            .onMouseLeave { isHovered = false }
    ) {
        // Menu Item
        Row(
            modifier = Modifier
                .padding(leftRight = 12.px, topBottom = 8.px)
                .cursor(Cursor.Pointer),
            verticalAlignment = Alignment.CenterVertically
        ) {
            SpanText(
                category.name,
                modifier = Modifier
                    .color(Color("#333333"))
                    .fontSize(14.px)
                    .fontWeight(600)
            )
            
            FaCaretDown(
                modifier = Modifier
                    .color(Color("#333333"))
                    .margin(left = 4.px)
                    .fontSize(12.px)
            )
        }
        
        // Dropdown Menu
        if (isHovered && category.subcategories.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .position(Position.Absolute)
                    .top(100.percent)
                    .left(0.px)
                    .maxWidth(90.vw)
                    .width(800.px)
                    .backgroundColor(Colors.White)
                    .boxShadow(0.px, 4.px, 8.px, 0.px, Color("#DD000000"))
                    .zIndex(1000)
                    .visibility(if (isHovered) Visibility.Visible else Visibility.Hidden)
                    .border(1.px, LineStyle.Solid, Color("#e0e0e0"))
            ) {
                SubcategoryMenu(category.subcategories)
            }
        }
    }
}

@Composable
private fun MobileNavMenuItem(category: MenuCategory) {
    var isExpanded by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // Category header
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.px)
                .backgroundColor(if (isExpanded) Color("#f5f5f5") else Colors.Transparent)
                .cursor(Cursor.Pointer)
                .onClick { isExpanded = !isExpanded },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            SpanText(
                category.name,
                modifier = Modifier
                    .color(Color("#333333"))
                    .fontSize(16.px)
                    .fontWeight(600)
            )
            
            if (category.subcategories.isNotEmpty()) {
                if (isExpanded) {
                    FaCaretDown(
                        modifier = Modifier
                            .color(Color("#4CAF50"))
                            .fontSize(16.px)
                    )
                } else {
                    FaChevronRight(
                        modifier = Modifier
                            .color(Color("#4CAF50"))
                            .fontSize(16.px)
                    )
                }
            }
        }
        
        // Subcategories
        if (isExpanded && category.subcategories.isNotEmpty()) {
            category.subcategories.forEach { (subcategoryName, items) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(leftRight = 32.px, topBottom = 8.px)
                        .cursor(Cursor.Pointer),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    SpanText(
                        subcategoryName,
                        modifier = Modifier
                            .color(Color("#4CAF50"))
                            .fontSize(14.px)
                            .fontWeight(600)
                    )
                }
                
                if (items.isNotEmpty()) {
                    items.forEach { item ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(leftRight = 48.px, topBottom = 6.px)
                                .cursor(Cursor.Pointer),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            SpanText(
                                item,
                                modifier = Modifier
                                    .color(Color("#666666"))
                                    .fontSize(14.px)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun SubcategoryMenu(subcategories: Map<String, List<String>>) {
    var hoveredCategory by remember { mutableStateOf<String?>(null) }
    val breakpoint = rememberBreakpoint()

    Row(
        modifier = Modifier.fillMaxWidth().padding(16.px)
    ) {
        // Left column - Main categories
        Column(
            modifier = Modifier
                .width(if (breakpoint >= Breakpoint.LG) 30.percent else 40.percent)
                .borderRight(1.px, LineStyle.Solid, Color("#e0e0e0"))
                .padding(right = 16.px)
        ) {
            subcategories.keys.forEach { mainCategory ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(topBottom = 8.px)
                        .cursor(Cursor.Pointer)
                        .backgroundColor(if (hoveredCategory == mainCategory) Color("#f5f5f5") else Colors.Transparent)
                        .onMouseEnter { hoveredCategory = mainCategory }
                        .onClick { hoveredCategory = mainCategory },
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    SpanText(
                        mainCategory,
                        modifier = Modifier
                            .color(Color("#333333"))
                            .fontSize(14.px)
                            .fontWeight(600)
                    )
                    
                    val hasSubcategories = subcategories[mainCategory]?.isNotEmpty() ?: false
                    if (hasSubcategories) {
                        FaChevronRight(
                            modifier = Modifier
                                .color(Color("#4CAF50"))
                                .fontSize(12.px)
                        )
                    }
                }
            }
        }
        
        // Right column - Subcategories for the hovered category
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(left = 16.px)
        ) {
            val selectedSubcategories = if (hoveredCategory != null) {
                subcategories[hoveredCategory] ?: emptyList()
            } else {
                emptyList()
            }
            
            if (hoveredCategory == null) {
                // If no category is hovered yet, show a message
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(topBottom = 6.px),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    SpanText(
                        "Hover over a category to see subcategories",
                        modifier = Modifier
                            .color(Color("#666666"))
                            .fontSize(14.px)
                            .fontStyle(FontStyle.Italic)
                    )
                }
            } else if (selectedSubcategories.isEmpty()) {
                // If the selected category has no subcategories
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(topBottom = 6.px),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    SpanText(
                        "Browse all products in $hoveredCategory",
                        modifier = Modifier
                            .color(Color("#666666"))
                            .fontSize(14.px)
                            .fontStyle(FontStyle.Italic)
                    )
                }
            } else {
                // Show the subcategories for the selected category
                selectedSubcategories.forEach { subCategory ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(topBottom = 6.px)
                            .cursor(Cursor.Pointer),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        SpanText(
                            subCategory,
                            modifier = Modifier
                                .color(Color("#666666"))
                                .fontSize(14.px)
                        )
                    }
                }
            }
        }
    }
} 