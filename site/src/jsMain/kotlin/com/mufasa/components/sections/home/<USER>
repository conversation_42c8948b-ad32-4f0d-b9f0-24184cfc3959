package com.varabyte.kobweb.site.components.sections.home

import androidx.compose.runtime.*
import com.varabyte.kobweb.compose.css.CSSPosition
import com.varabyte.kobweb.compose.css.FontWeight
import com.varabyte.kobweb.compose.css.TextAlign
import com.varabyte.kobweb.compose.css.functions.RadialGradient
import com.varabyte.kobweb.compose.css.functions.radialGradient
import com.varabyte.kobweb.compose.foundation.layout.Box
import com.varabyte.kobweb.compose.foundation.layout.Column
import com.varabyte.kobweb.compose.ui.Modifier
import com.varabyte.kobweb.compose.ui.graphics.Color
import com.varabyte.kobweb.compose.ui.graphics.Colors
import com.varabyte.kobweb.compose.ui.modifiers.*
import com.varabyte.kobweb.silk.components.layout.SimpleGrid
import com.varabyte.kobweb.silk.components.layout.numColumns
import com.varabyte.kobweb.silk.style.*
import com.varabyte.kobweb.silk.components.text.SpanText
import com.varabyte.kobweb.silk.theme.colors.ColorMode
import com.varabyte.kobweb.site.components.style.MutedSpanTextVariant
import com.varabyte.kobweb.site.components.style.boxShadow
import com.varabyte.kobweb.site.components.widgets.Section
import org.jetbrains.compose.web.css.*
import org.jetbrains.compose.web.dom.*

fun Modifier.background(colorMode: ColorMode) =
    this.then(when (colorMode) {
//        ColorMode.DARK -> Modifier.backgroundImage(
//            radialGradient(RadialGradient.Shape.Circle, Color.rgb(41, 41, 46), Color.rgb(25, 25, 28), CSSPosition.Top)
//        )
        ColorMode.DARK -> Modifier.backgroundColor(Colors.White)
        ColorMode.LIGHT -> Modifier.backgroundColor(Colors.White)
    })


private class Feature(val heading: String, val desc: String)

val FeatureItemStyle = CssStyle.base {
    Modifier.margin(18.px)
}

@Composable
private fun FeatureItem(feature: Feature) {
    val colorMode by ColorMode.currentState

    Box (
        FeatureItemStyle.toModifier().then(Modifier
            .borderRadius(12.px)
            .background(colorMode)
            .padding(2.em)
            .boxShadow(colorMode)
        )
    ) {
        Column {
            SpanText(feature.heading, Modifier.fontWeight(FontWeight.Bold).margin(bottom = 0.75.cssRem))
            SpanText(feature.desc, Modifier.lineHeight(1.5), MutedSpanTextVariant)
        }
    }
}

@Composable
fun FeaturesSection() {
    val features = remember {
        listOf(
            Feature("Kotlin", "Unlock the Power of Kotlin – Write Clean, Concise, and Robust Code for Modern Applications!"),
            Feature("Android", "Start Your Android Journey – Build Your First Apps with Confidence and Ease!"),
            Feature("Jetpack Compose", "Design Dynamic Android UIs with Ease – Your Ultimate Guide to Jetpack Compose!"),
            Feature("Learn Once, Deploy Everywhere", "Discover the power of shared code for Android, iOS, Desktop, and beyond."),
            Feature("Simplify UI Development", "Create beautiful, responsive UIs with Compose Multiplatform’s intuitive toolkit."),
            Feature("Future-Proof Your Skills", "Stay ahead in cross-platform development with cutting-edge Kotlin expertise."),
            Feature("Simplifies Team Collaboration", "Streamlines work between designers, developers, and testers by unifying the development pipeline."),
            Feature("Leverages Powerful Tooling", "Benefit from advanced IDE support, debugging tools, and previews in IntelliJ IDEA and Android Studio."),
            Feature("Seamless Integration with Libraries", "Use multiplatform libraries like Ktor, SQLDelight, and Koin to handle networking, databases, and dependencies."),
            Feature("Optimized Performance", "With Compose Multiplatform, apps retain native performance across platforms."),
            Feature("Growing Community and Resources", "Access a supportive community, detailed documentation, and tutorials to guide your learning journey."),
            Feature("High Demand in the Industry", "Cross-platform skills are highly sought-after, and mastering Compose Multiplatform positions you as a versatile developer."),
        )
    }

    Section {
        H2 {
            SpanText(
                "What you will learrn",
                Modifier.textAlign(TextAlign.Center)
            )
        }
        SpanText(
            "Build your Compose apps quicker and easier",
            Modifier
                .lineHeight(1.5)
                .fontSize(1.25.cssRem)
                .textAlign(TextAlign.Center),
            MutedSpanTextVariant
        )

        SimpleGrid(numColumns(1, md = 3)) {
            features.forEach { feature -> FeatureItem(feature) }
        }
    }
}
