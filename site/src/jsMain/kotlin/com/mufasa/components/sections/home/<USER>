package com.varabyte.kobweb.site.components.sections.home

import androidx.compose.runtime.Composable
import com.varabyte.kobweb.compose.css.Cursor
import com.varabyte.kobweb.compose.foundation.layout.Arrangement
import com.varabyte.kobweb.compose.foundation.layout.Row
import com.varabyte.kobweb.compose.ui.Alignment
import com.varabyte.kobweb.compose.ui.Modifier
import com.varabyte.kobweb.compose.ui.graphics.Color
import com.varabyte.kobweb.compose.ui.graphics.Colors
import com.varabyte.kobweb.compose.ui.modifiers.*
import com.varabyte.kobweb.silk.components.graphics.Image
import com.varabyte.kobweb.silk.components.text.SpanText
import com.varabyte.kobweb.silk.style.breakpoint.Breakpoint
import com.varabyte.kobweb.silk.theme.breakpoint.rememberBreakpoint
import org.jetbrains.compose.web.css.*
import org.jetbrains.compose.web.dom.Button

@Composable
fun NavBar() {
    val breakpoint = rememberBreakpoint()

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                topBottom = 16.px,
                leftRight = if (breakpoint >= Breakpoint.MD) 80.px else 20.px
            )
            .backgroundColor(Color("#f8f9fa")),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Logo and Brand Name
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.px)
        ) {
            // Mufasa Logo (globe icon)
            Image(
                src = "/images/logo.png",
                modifier = Modifier
                    .size(40.px)
                    .cursor(Cursor.Pointer)
            )

            SpanText(
                "Mufasa.pk",
                modifier = Modifier
                    .fontSize(24.px)
                    .fontWeight(700)
                    .color(Color("#333333"))
                    .cursor(Cursor.Pointer)
            )
        }

        // Navigation Menu - only visible on larger screens
        if (breakpoint >= Breakpoint.MD) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(32.px),
                verticalAlignment = Alignment.CenterVertically
            ) {
                SpanText(
                    "Home",
                    modifier = Modifier
                        .fontSize(16.px)
                        .fontWeight(500)
                        .color(Color("#333333"))
                        .cursor(Cursor.Pointer)
                )

                SpanText(
                    "About Us",
                    modifier = Modifier
                        .fontSize(16.px)
                        .fontWeight(500)
                        .color(Color("#333333"))
                        .cursor(Cursor.Pointer)
                )

                SpanText(
                    "Price",
                    modifier = Modifier
                        .fontSize(16.px)
                        .fontWeight(500)
                        .color(Color("#333333"))
                        .cursor(Cursor.Pointer)
                )

                SpanText(
                    "Login",
                    modifier = Modifier
                        .fontSize(16.px)
                        .fontWeight(500)
                        .color(Color("#333333"))
                        .cursor(Cursor.Pointer)
                )
            }
        }

        // Start for free button
        Button(
            attrs = {
                style {
                    backgroundColor(Colors.Transparent)
                    color(Color("#333333"))
                    border(2.px, LineStyle.Solid, Color("#333333"))
                    borderRadius(8.px)
                    padding(12.px, 24.px)
                    cursor("pointer")
                    fontSize(16.px)
                    fontWeight(500)
                }
            }
        ) {
            SpanText("Start for free")
        }
    }
}