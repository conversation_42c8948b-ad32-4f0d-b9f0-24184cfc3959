package com.varabyte.kobweb.site.components.sections.home

import androidx.compose.runtime.*
import com.varabyte.kobweb.compose.css.Cursor
import com.varabyte.kobweb.compose.css.TextAlign
import com.varabyte.kobweb.compose.foundation.layout.Arrangement
import com.varabyte.kobweb.compose.foundation.layout.Column
import com.varabyte.kobweb.compose.foundation.layout.Row
import com.varabyte.kobweb.compose.ui.Alignment
import com.varabyte.kobweb.compose.ui.Modifier
import com.varabyte.kobweb.compose.ui.graphics.Colors
import com.varabyte.kobweb.compose.ui.modifiers.*
import com.varabyte.kobweb.silk.components.graphics.Image
import com.varabyte.kobweb.silk.components.icons.fa.*
import com.varabyte.kobweb.silk.components.text.SpanText
import com.varabyte.kobweb.silk.style.breakpoint.Breakpoint
import com.varabyte.kobweb.silk.theme.breakpoint.rememberBreakpoint
import org.jetbrains.compose.web.css.*

@Composable
fun FooterSection() {
    val breakpoint = rememberBreakpoint()
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .backgroundColor(Color("#4CAF50"))
            .padding(
                topBottom = if (breakpoint >= Breakpoint.MD) 40.px else 24.px,
                leftRight = if (breakpoint >= Breakpoint.MD) 50.px else 16.px
            ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (breakpoint >= Breakpoint.MD) {
            // Desktop layout - three columns side by side
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .margin(bottom = 30.px),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // Logo and About
                Column(
                    modifier = Modifier.width(25.percent)
                ) {
                    Image(
                        src = "/images/martaba_institute_white.png",
                        modifier = Modifier
                            .height(40.px)
                            .margin(bottom = 16.px)
                    )
                    SpanText(
                        "Mufasa",
                        modifier = Modifier
                            .color(Colors.White)
                            .fontSize(14.px)
                            .lineHeight(1.6)
                    )
                }
                
                // Quick Links
                Column(
                    modifier = Modifier.width(20.percent)
                ) {
                    SpanText(
                        "Quick Links",
                        modifier = Modifier
                            .color(Colors.White)
                            .fontSize(18.px)
                            .fontWeight(700)
                            .margin(bottom = 16.px)
                    )
                    
                    FooterLink("Home")
                    FooterLink("Courses")
                    FooterLink("About Us")
                    FooterLink("Contact")
                }
                
                // Contact
                Column(
                    modifier = Modifier.width(25.percent)
                ) {
                    SpanText(
                        "Contact Us",
                        modifier = Modifier
                            .color(Colors.White)
                            .fontSize(18.px)
                            .fontWeight(700)
                            .margin(bottom = 16.px)
                    )
                    
                    Row(
                        modifier = Modifier.margin(bottom = 8.px),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        FaEnvelope(
                            modifier = Modifier
                                .color(Colors.White)
                                .margin(right = 8.px)
                        )
                        SpanText(
                            "<EMAIL>",
                            modifier = Modifier
                                .color(Colors.White)
                                .fontSize(14.px)
                        )
                    }
                    
                    Row(
                        modifier = Modifier.margin(bottom = 8.px),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        FaPhone(
                            modifier = Modifier
                                .color(Colors.White)
                                .margin(right = 8.px)
                        )
                        SpanText(
                            "+92 03 700 MUFASA",
                            modifier = Modifier
                                .color(Colors.White)
                                .fontSize(14.px)
                        )
                    }
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        SpanText(
                            "123 Mufasa Street, Karachi, Pakistan",
                            modifier = Modifier
                                .color(Colors.White)
                                .fontSize(14.px)
                        )
                    }
                }
            }
        } else {
            // Mobile layout - stacked columns
            
            // Logo and About
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .margin(bottom = 24.px),
                horizontalAlignment = Alignment.Start
            ) {
                Image(
                    src = "/images/martaba_institute_white.png",
                    modifier = Modifier
                        .height(40.px)
                        .margin(bottom = 16.px)
                )
                SpanText(
                    "Mufasa",
                    modifier = Modifier
                        .color(Colors.White)
                        .fontSize(14.px)
                        .lineHeight(1.6)
                )
            }
            
            // Quick Links
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .margin(bottom = 24.px),
                horizontalAlignment = Alignment.Start
            ) {
                SpanText(
                    "Quick Links",
                    modifier = Modifier
                        .color(Colors.White)
                        .fontSize(18.px)
                        .fontWeight(700)
                        .margin(bottom = 16.px)
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    FooterLink("Home")
                    FooterLink("Courses")
                    FooterLink("About Us")
                    FooterLink("Contact")
                }
            }
            
            // Contact
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .margin(bottom = 24.px),
                horizontalAlignment = Alignment.Start
            ) {
                SpanText(
                    "Contact Us",
                    modifier = Modifier
                        .color(Colors.White)
                        .fontSize(18.px)
                        .fontWeight(700)
                        .margin(bottom = 16.px)
                )
                
                Row(
                    modifier = Modifier.margin(bottom = 8.px),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    FaEnvelope(
                        modifier = Modifier
                            .color(Colors.White)
                            .margin(right = 8.px)
                    )
                    SpanText(
                        "<EMAIL>",
                        modifier = Modifier
                            .color(Colors.White)
                            .fontSize(14.px)
                    )
                }
                
                Row(
                    modifier = Modifier.margin(bottom = 8.px),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    FaPhone(
                        modifier = Modifier
                            .color(Colors.White)
                            .margin(right = 8.px)
                    )
                    SpanText(
                        "****** 567 8900",
                        modifier = Modifier
                            .color(Colors.White)
                            .fontSize(14.px)
                    )
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    SpanText(
                        "123 Education St, Learning City",
                        modifier = Modifier
                            .color(Colors.White)
                            .fontSize(14.px)
                    )
                }
            }
        }
        
        // Copyright - same for both layouts
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .borderTop(1.px, LineStyle.Solid, rgba(255, 255, 255, 0.2))
                .padding(top = 20.px),
            horizontalArrangement = Arrangement.Center
        ) {
            SpanText(
                "© 2025 Mufasa. All rights reserved.",
                modifier = Modifier
                    .color(Colors.White)
                    .fontSize(14.px)
                    .textAlign(TextAlign.Center)
            )
        }
    }
}

@Composable
private fun FooterLink(text: String) {
    val breakpoint = rememberBreakpoint()
    
    SpanText(
        text,
        modifier = Modifier
            .color(Colors.White)
            .fontSize(14.px)
            .margin(
                bottom = if (breakpoint >= Breakpoint.MD) 8.px else 0.px,
                right = if (breakpoint < Breakpoint.MD) 16.px else 0.px
            )
            .cursor(Cursor.Pointer)
    )
} 