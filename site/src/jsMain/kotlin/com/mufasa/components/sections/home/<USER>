package com.varabyte.kobweb.site.components.sections.home

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.varabyte.kobweb.compose.css.Cursor
import com.varabyte.kobweb.compose.css.FontStyle
import com.varabyte.kobweb.compose.css.Overflow
import com.varabyte.kobweb.compose.css.ScrollBehavior
import com.varabyte.kobweb.compose.foundation.layout.Arrangement
import com.varabyte.kobweb.compose.foundation.layout.Box
import com.varabyte.kobweb.compose.foundation.layout.Column
import com.varabyte.kobweb.compose.foundation.layout.Row
import com.varabyte.kobweb.compose.ui.Alignment
import com.varabyte.kobweb.compose.ui.Modifier
import com.varabyte.kobweb.compose.ui.graphics.Colors
import com.varabyte.kobweb.compose.ui.modifiers.*
import com.varabyte.kobweb.silk.components.icons.fa.FaChevronRight
import com.varabyte.kobweb.silk.components.text.SpanText
import org.jetbrains.compose.web.css.*

data class CategoryItem(
    val name: String,
    val subcategories: List<String> = emptyList()
)

@Composable
fun CategoryNavBar() {
    var selectedCategoryIndex by remember { mutableStateOf(0) }
    var hoveredCategoryIndex by remember { mutableStateOf<Int?>(null) }
    
    val categories = remember {
        listOf(
            CategoryItem("Animal Feed Products"),
            CategoryItem("Bye Products", listOf(
                "Rice Husk", "Sugar Cane bye products", "Soap Stock", "Acid Oil", 
                "Soya Acid Oil", "Crushed Plastic", "Transformers' Used Oil"
            )),
            CategoryItem("Pet Bottles"),
            CategoryItem("Grain Products"),
            CategoryItem("Moallam ul Quran with pen"),
            CategoryItem("Kids Collection", listOf(
                "Garments", "Toys", "Educational", "Watches", "Goggles", "School Bags",
                "Story Books", "Art & Craft", "Soft Toys", "Birthday Decorations"
            )),
            CategoryItem("Cosmetics / Body Products"),
            CategoryItem("Home Decor"),
            CategoryItem("Garden Decor / Gardening Items"),
            CategoryItem("Mobile Accessories"),
            CategoryItem("Laptop Accessories"),
            CategoryItem("Ladies Purse"),
            CategoryItem("Mehendi Decorations"),
            CategoryItem("Perfumes", listOf(
                "MTJ", "Scents & Stories", "J.", "Saeed Ghani"
            )),
            CategoryItem("Himalayan Salt Products", listOf(
                "Lamps", "Paper weight", "Show Pieces"
            )),
            CategoryItem("Gents", listOf(
                "Wallets", "Leather Belts", "Joggers", "Smart Watches", "Watches",
                "Goggles", "Car Accessories", "Gift Packs"
            )),
            CategoryItem("Prayers Accessories", listOf(
                "Tasbeeh", "Prayer Carpet / Jae Namaz", "Prayer Chair", "Rehel",
                "Quran Box", "Abaya", "Ahraam"
            )),
            CategoryItem("Travelling Bags"),
            CategoryItem("Marble Show Pieces"),
            CategoryItem("Bed Sheets"),
            CategoryItem("Disposible Items", listOf(
                "Paper Cups", "Paper Glass", "Paper Plates (Small / Medium / Large)"
            ))
        )
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(400.px)
            .backgroundColor(Colors.White)
            .borderBottom(1.px, LineStyle.Solid, Color("#e0e0e0"))
    ) {
        Row(
            modifier = Modifier.fillMaxSize()
        ) {
            // Left column - Main categories
            Column(
                modifier = Modifier
                    .fillMaxHeight()
                    .width(30.percent)
                    .borderRight(1.px, LineStyle.Solid, Color("#e0e0e0"))
                    .overflow(Overflow.Auto)
                    .scrollBehavior(ScrollBehavior.Smooth),
            ) {
                categories.forEachIndexed { index, category ->
                    CategoryItem(
                        name = category.name,
                        isSelected = index == selectedCategoryIndex,
                        isHovered = index == hoveredCategoryIndex,
                        onClick = { selectedCategoryIndex = index },
                        onMouseEnter = { hoveredCategoryIndex = index },
                        onMouseLeave = { hoveredCategoryIndex = null }
                    )
                }
            }
            
            // Right column - Subcategories
            Column(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth()
                    .padding(leftRight = 16.px)
                    .overflow(Overflow.Auto)
                    .scrollBehavior(ScrollBehavior.Smooth),
                verticalArrangement = Arrangement.Top
            ) {
                // Determine which category to show subcategories for
                val displayCategoryIndex = hoveredCategoryIndex ?: selectedCategoryIndex
                val currentCategory = categories[displayCategoryIndex]
                
                if (currentCategory.subcategories.isEmpty()) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.px),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        SpanText(
                            text = "No subcategories available for ${currentCategory.name}",
                            modifier = Modifier
                                .color(Color("#666666"))
                                .fontSize(16.px)
                                .fontStyle(FontStyle.Italic)
                        )
                    }
                } else {
                    SpanText(
                        text = "Subcategories of ${currentCategory.name}",
                        modifier = Modifier
                            .padding(16.px)
                            .color(Color("#4CAF50"))
                            .fontSize(18.px)
                            .fontWeight(600)
                    )
                    
                    currentCategory.subcategories.forEach { subcategory ->
                        SubcategoryItem(name = subcategory)
                    }
                }
            }
        }
    }
}

@Composable
private fun CategoryItem(
    name: String, 
    isSelected: Boolean, 
    isHovered: Boolean,
    onClick: () -> Unit,
    onMouseEnter: () -> Unit,
    onMouseLeave: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.px)
            .backgroundColor(
                when {
                    isSelected -> Color("#4CAF50")
                    isHovered -> Color("#E8F5E9") // Light green for hover state
                    else -> Colors.Transparent
                }
            )
            .color(if (isSelected) Colors.White else Color("#333333"))
            .cursor(Cursor.Pointer)
            .onClick { onClick() }
            .onMouseEnter { onMouseEnter() }
            .onMouseLeave { onMouseLeave() },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        SpanText(
            text = name,
            modifier = Modifier
                .fontWeight(if (isSelected || isHovered) 600 else 400)
                .fontSize(16.px)
        )
        
        if (isSelected) {
            FaChevronRight(
                modifier = Modifier
                    .color(Colors.White)
                    .fontSize(14.px)
            )
        }
    }
}

@Composable
private fun SubcategoryItem(name: String) {
    var isHovered by remember { mutableStateOf(false) }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.px)
            .backgroundColor(if (isHovered) Color("#F5F5F5") else Colors.Transparent)
            .cursor(Cursor.Pointer)
            .onMouseEnter { isHovered = true }
            .onMouseLeave { isHovered = false },
        verticalAlignment = Alignment.CenterVertically
    ) {
        SpanText(
            text = name,
            modifier = Modifier
                .color(Color("#333333"))
                .fontSize(16.px)
        )
    }
} 