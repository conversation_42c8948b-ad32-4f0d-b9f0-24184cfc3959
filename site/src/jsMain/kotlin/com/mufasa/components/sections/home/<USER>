package com.varabyte.kobweb.site.components.sections.home

import androidx.compose.runtime.*
import com.varabyte.kobweb.compose.css.*
import com.varabyte.kobweb.compose.foundation.layout.Arrangement
import com.varabyte.kobweb.compose.foundation.layout.Box
import com.varabyte.kobweb.compose.foundation.layout.Column
import com.varabyte.kobweb.compose.foundation.layout.Row
import com.varabyte.kobweb.compose.ui.Alignment
import com.varabyte.kobweb.compose.ui.Modifier
import com.varabyte.kobweb.compose.ui.graphics.Colors
import com.varabyte.kobweb.compose.ui.modifiers.*
import com.varabyte.kobweb.silk.components.graphics.Image
import com.varabyte.kobweb.silk.components.layout.SimpleGrid
import com.varabyte.kobweb.silk.components.layout.numColumns
import com.varabyte.kobweb.silk.components.text.SpanText
import com.varabyte.kobweb.silk.style.breakpoint.Breakpoint
import com.varabyte.kobweb.silk.theme.breakpoint.rememberBreakpoint
import org.jetbrains.compose.web.css.*
import org.jetbrains.compose.web.dom.H2

@Composable
fun CategoriesSection() {
    val breakpoint = rememberBreakpoint()
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                topBottom = if (breakpoint >= Breakpoint.MD) 50.px else 30.px,
                leftRight = if (breakpoint >= Breakpoint.MD) 50.px else 16.px
            )
            .backgroundColor(Colors.White),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Categories Header
        Box(
            Modifier
                .fillMaxWidth()
                .padding(bottom = if (breakpoint >= Breakpoint.MD) 32.px else 20.px),
        ) {
            H2 {
                SpanText(
                    "Categories",
                    Modifier
                        .color(Color("#333333"))
                        .fontSize(if (breakpoint >= Breakpoint.MD) 32.px else 24.px)
                        .fontWeight(700)
                )
            }
        }
        
        // Category Cards
        SimpleGrid(
            numColumns(1, sm = 2, md = 3, lg = 6),
            modifier = Modifier
                .fillMaxWidth()
                .gap(if (breakpoint >= Breakpoint.MD) 16.px else 10.px)
        ) {
            CategoryCard(
                "Animal Feed Products",
                "https://gadot.com/wp-content/uploads/2020/10/animal-nut.jpg",
                breakpoint
            )
            CategoryCard(
                "Kids Collection",
                "https://images.unsplash.com/photo-1594736797933-d0501ba2fe65?q=80&w=1974&auto=format&fit=crop",
                breakpoint
            )
            CategoryCard(
                "Home Decor",
                "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?q=80&w=1932&auto=format&fit=crop",
                breakpoint
            )
            CategoryCard(
                "Cosmetics",
                "https://images.unsplash.com/photo-1571781926291-c477ebfd024b?q=80&w=1976&auto=format&fit=crop",
                breakpoint
            )
            CategoryCard(
                "Electronics",
                "https://images.unsplash.com/photo-1550009158-9ebf69173e03?q=80&w=1974&auto=format&fit=crop",
                breakpoint
            )
            CategoryCard(
                "Religious Items",
                "https://www.shutterstock.com/image-photo/aladdin-lamp-koran-muslim-lantern-260nw-2259810257.jpg",
                breakpoint
            )
        }
    }
}

@Composable
private fun CategoryCard(title: String, imageUrl: String, breakpoint: Breakpoint) {
    Column(
        modifier = Modifier
            .borderRadius(8.px)
            .border(1.px, LineStyle.Solid, Color("#e0e0e0"))
            .backgroundColor(Colors.White)
            .cursor(Cursor.Pointer),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Image
        Box(
            Modifier
                .fillMaxWidth()
                .height(if (breakpoint >= Breakpoint.MD) 150.px else 120.px)
                .overflow(Overflow.Hidden)
        ) {
            Image(
                src = imageUrl,
                modifier = Modifier
                    .fillMaxSize()
                    .objectFit(ObjectFit.Cover)
            )
        }
        
        // Title
        SpanText(
            title,
            modifier = Modifier
                .fillMaxWidth()
                .padding(if (breakpoint >= Breakpoint.MD) 12.px else 8.px)
                .textAlign(TextAlign.Center)
                .color(Color("#333333"))
                .fontSize(if (breakpoint >= Breakpoint.MD) 16.px else 14.px)
                .fontWeight(600)
        )
    }
} 