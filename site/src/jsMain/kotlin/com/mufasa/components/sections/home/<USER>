package com.varabyte.kobweb.site.components.sections.home

import androidx.compose.runtime.*
import com.varabyte.kobweb.compose.css.Cursor
import com.varabyte.kobweb.compose.css.ObjectFit
import com.varabyte.kobweb.compose.foundation.layout.Arrangement
import com.varabyte.kobweb.compose.foundation.layout.Box
import com.varabyte.kobweb.compose.foundation.layout.Column
import com.varabyte.kobweb.compose.foundation.layout.Row
import com.varabyte.kobweb.compose.ui.Alignment
import com.varabyte.kobweb.compose.ui.Modifier
import com.varabyte.kobweb.compose.ui.graphics.Color
import com.varabyte.kobweb.compose.ui.graphics.Colors
import com.varabyte.kobweb.compose.ui.modifiers.*
import com.varabyte.kobweb.silk.components.graphics.Image
import com.varabyte.kobweb.silk.components.icons.fa.FaEnvelope
import com.varabyte.kobweb.silk.components.icons.fa.FaMap
import com.varabyte.kobweb.silk.components.icons.fa.FaPhone
import com.varabyte.kobweb.silk.components.text.SpanText
import com.varabyte.kobweb.silk.style.breakpoint.Breakpoint
import com.varabyte.kobweb.silk.theme.breakpoint.rememberBreakpoint
import org.jetbrains.compose.web.css.*
import org.jetbrains.compose.web.dom.Button

@Composable
fun BannerSection() {
    val breakpoint = rememberBreakpoint()

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .backgroundColor(Color("#f8f9fa"))
            .padding(
                topBottom = if (breakpoint >= Breakpoint.MD) 80.px else 40.px,
                leftRight = if (breakpoint >= Breakpoint.MD) 80.px else 20.px
            )
    ) {
        if (breakpoint >= Breakpoint.MD) {
            // Desktop Layout
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Left Content
                Column(
                    modifier = Modifier.width(50.percent),
                    horizontalAlignment = Alignment.Start
                ) {
                    HeroContent()
                }

                // Right Image
                Box(
                    modifier = Modifier
                        .width(45.percent)
                        .height(400.px)
                        .borderRadius(20.px)
                        .backgroundColor(Color("#e8e8e8")),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        src = "https://images.unsplash.com/photo-1521791136064-7986c2920216?q=80&w=2069&auto=format&fit=crop",
                        modifier = Modifier
                            .fillMaxSize()
                            .borderRadius(20.px)
                            .objectFit(ObjectFit.Cover)
                    )
                }
            }
        } else {
            // Mobile Layout
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Image first on mobile
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(250.px)
                        .borderRadius(20.px)
                        .backgroundColor(Color("#e8e8e8"))
                        .margin(bottom = 32.px),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        src = "https://images.unsplash.com/photo-1521791136064-7986c2920216?q=80&w=2069&auto=format&fit=crop",
                        modifier = Modifier
                            .fillMaxSize()
                            .borderRadius(20.px)
                            .objectFit(ObjectFit.Cover)
                    )
                }

                // Content below on mobile
                HeroContent()
            }
        }
    }
}

@Composable
private fun HeroContent() {
    val breakpoint = rememberBreakpoint()

    Column(
        horizontalAlignment = Alignment.Start
    ) {
        // Main Heading
        SpanText(
            "Your Brand,",
            modifier = Modifier
                .fontSize(if (breakpoint >= Breakpoint.MD) 56.px else 36.px)
                .fontWeight(800)
                .color(Color("#333333"))
                .lineHeight(1.1)
                .margin(bottom = 0.px)
        )

        SpanText(
            "Your Future —",
            modifier = Modifier
                .fontSize(if (breakpoint >= Breakpoint.MD) 56.px else 36.px)
                .fontWeight(800)
                .color(Color("#333333"))
                .lineHeight(1.1)
                .margin(bottom = 0.px)
        )

        SpanText(
            "Online.",
            modifier = Modifier
                .fontSize(if (breakpoint >= Breakpoint.MD) 56.px else 36.px)
                .fontWeight(800)
                .color(Color("#333333"))
                .lineHeight(1.1)
                .margin(bottom = 32.px)
        )

        // Description
        SpanText(
            "Take your business to the next level with a powerful online presence. From social media to Google Maps to website, we help you get found everywhere. Grow your brand, boost your visibility, and connect with more customers today.",
            modifier = Modifier
                .fontSize(if (breakpoint >= Breakpoint.MD) 18.px else 16.px)
                .color(Color("#666666"))
                .lineHeight(1.6)
                .margin(bottom = 40.px)
                .maxWidth(if (breakpoint >= Breakpoint.MD) 500.px else 100.percent)
        )

        // CTA Button and Contact Info
        Column(
            verticalArrangement = Arrangement.spacedBy(24.px)
        ) {
            // Register Now Button
            Button(
                attrs = {
                    style {
                        backgroundColor(Color("#000000"))
                        color(Colors.White)
                        border(0.px)
                        borderRadius(50.px)
                        padding(16.px, 32.px)
                        cursor("pointer")
                        fontSize(16.px)
                        fontWeight(600)
                    }
                }
            ) {
                SpanText("REGISTER NOW")
            }

            // Contact Information
            Column(
                verticalArrangement = Arrangement.spacedBy(12.px)
            ) {
                ContactItem(
                    icon = { FaPhone(modifier = Modifier.fontSize(16.px).color(Color("#333333"))) },
                    text = "+92 03 700 MUFASA"
                )

                ContactItem(
                    icon = { FaEnvelope(modifier = Modifier.fontSize(16.px).color(Color("#333333"))) },
                    text = "<EMAIL>"
                )

                ContactItem(
                    icon = { FaMap(modifier = Modifier.fontSize(16.px).color(Color("#333333"))) },
                    text = "Empire Center, Gulistan-e-Johar, Karachi"
                )
            }
        }
    }
}

@Composable
private fun ContactItem(
    icon: @Composable () -> Unit,
    text: String
) {
    val breakpoint = rememberBreakpoint()

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.px)
    ) {
        Box(
            modifier = Modifier
                .size(32.px)
                .borderRadius(50.percent)
                .backgroundColor(Color("#f0f0f0")),
            contentAlignment = Alignment.Center
        ) {
            icon()
        }

        SpanText(
            text,
            modifier = Modifier
                .fontSize(if (breakpoint >= Breakpoint.MD) 16.px else 14.px)
                .color(Color("#333333"))
                .fontWeight(500)
        )
    }
}